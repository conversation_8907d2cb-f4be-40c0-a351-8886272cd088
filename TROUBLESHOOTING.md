 # NVH Django 项目故障排除指南

## 常见启动问题及解决方案

### 1. SCSS 循环导入错误
**错误信息**: `This file is already being loaded`

**解决方案**: 
- 已修复 `vue.config.js` 中的 SCSS 配置
- 移除了循环导入，改为直接在配置中定义变量

### 2. 缺少 path-browserify 依赖
**错误信息**: `Can't resolve 'path-browserify'`

**解决方案**:
- 已添加 `path-browserify` 到 `package.json`
- 或者使用自定义路径解析函数（已实现）

### 3. ESLint 配置缺失
**错误信息**: `No ESLint configuration found`

**解决方案**:
- 已创建 `.eslintrc.js` 配置文件
- 配置了 Vue 3 和 ES2021 支持

### 4. 后端连接失败
**错误信息**: `ECONNREFUSED` 或代理错误

**解决方案**:
1. 确保后端服务正在运行
2. 检查端口 8000 是否被占用
3. 使用分步启动脚本

## 推荐启动流程

### 方法1：使用简化脚本（推荐）

```bash
# 1. 首次设置（只需运行一次）
start-simple.bat

# 2. 启动服务器
start-servers.bat
```

### 方法2：手动启动

**终端1 - 启动后端**:
```bash
cd backend
venv\Scripts\activate
python manage.py runserver 0.0.0.0:8000
```

**终端2 - 启动前端**:
```bash
cd frontend
npm run serve
```

## 常见问题检查清单

### 环境检查
- [ ] Python 3.9+ 已安装
- [ ] Node.js 16+ 已安装
- [ ] MySQL 服务正在运行
- [ ] 端口 8000 和 3000 未被占用

### 依赖检查
- [ ] 后端虚拟环境已创建
- [ ] Python 依赖已安装 (`pip install -r requirements.txt`)
- [ ] 前端依赖已安装 (`npm install`)
- [ ] 数据库迁移已执行 (`python manage.py migrate`)

### 配置检查
- [ ] `.env` 文件已配置（从 `.env.example` 复制）
- [ ] 数据库连接信息正确
- [ ] Keycloak 服务可访问

## 具体错误解决

### 数据库连接错误
```bash
# 检查 MySQL 服务
net start mysql

# 测试数据库连接
mysql -u root -p -e "SHOW DATABASES;"
```

### 端口占用问题
```bash
# 检查端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :3000

# 结束占用进程
taskkill /PID <进程ID> /F
```

### 权限问题
```bash
# 以管理员身份运行命令提示符
# 或者修改文件夹权限
```

### Keycloak 连接测试
```bash
# 测试 Keycloak 服务
curl -I https://account-test.sgmw.com.cn/auth/
```

## 开发建议

1. **首次启动**: 使用 `start-simple.bat` 进行初始设置
2. **日常开发**: 使用 `start-servers.bat` 快速启动
3. **调试模式**: 手动启动以查看详细日志
4. **生产部署**: 使用 Docker Compose

## 日志查看

### 后端日志
- Django 控制台输出
- `backend/logs/django.log`

### 前端日志
- 浏览器开发者工具控制台
- Vue CLI 服务器输出

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Python 和 Node.js 版本
3. 完整的错误信息
4. 执行的命令和步骤
