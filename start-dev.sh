#!/bin/bash

echo "Starting NVH Django Development Environment..."
echo

# 检查是否存在虚拟环境
if [ ! -d "backend/venv" ]; then
    echo "Creating Python virtual environment..."
    cd backend
    python3 -m venv venv
    cd ..
fi

# 启动后端服务
echo "Starting Django backend server..."
gnome-terminal --tab --title="Django Backend" -- bash -c "cd backend && source venv/bin/activate && pip install -r requirements.txt && python manage.py migrate && python manage.py runserver 0.0.0.0:8000; exec bash"

# 等待几秒让后端启动
sleep 5

# 启动前端服务
echo "Starting Vue frontend server..."
gnome-terminal --tab --title="Vue Frontend" -- bash -c "cd frontend && npm install && npm run serve; exec bash"

echo
echo "Development servers are starting..."
echo "Backend: http://localhost:8000"
echo "Frontend: http://localhost:3000"
echo
echo "Press Ctrl+C to exit..."

# 保持脚本运行
while true; do
    sleep 1
done
