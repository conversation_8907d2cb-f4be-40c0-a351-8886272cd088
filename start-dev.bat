@echo off
echo Starting NVH Django Development Environment...
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.9+ and try again
    pause
    exit /b 1
)

REM 检查 Node.js 是否安装
node --version >nul 2>&1
if errorlevel 1 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js 16+ and try again
    pause
    exit /b 1
)

REM 检查是否存在虚拟环境
if not exist "backend\venv" (
    echo Creating Python virtual environment...
    cd backend
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

REM 启动后端服务
echo Starting Django backend server...
start "Django Backend" cmd /k "cd /d %~dp0backend && venv\Scripts\activate && pip install -r requirements.txt && python manage.py migrate && echo Backend is ready! && python manage.py runserver 0.0.0.0:8000"

REM 等待后端启动
echo Waiting for backend to start...
timeout /t 10 /nobreak > nul

REM 检查后端是否启动成功
echo Checking backend status...
curl -s http://localhost:8000/api/core/health/ >nul 2>&1
if errorlevel 1 (
    echo Warning: Backend may not be ready yet. Frontend will start anyway.
    timeout /t 5 /nobreak > nul
)

REM 启动前端服务
echo Starting Vue frontend server...
start "Vue Frontend" cmd /k "cd /d %~dp0frontend && npm install && echo Frontend dependencies installed! && npm run serve"

echo.
echo Development servers are starting...
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo Wait for both servers to fully start before accessing the application.
echo Press any key to exit...
pause > nul
