@echo off
echo Starting NVH Django Servers...
echo.

echo Starting Django backend server...
start "Django Backend" cmd /k "cd /d %~dp0backend && venv\Scripts\activate && python manage.py runserver 0.0.0.0:8000"

echo Waiting 8 seconds for backend to start...
timeout /t 8 /nobreak > nul

echo Starting Vue frontend server...
start "Vue Frontend" cmd /k "cd /d %~dp0frontend && npm run serve"

echo.
echo Both servers are starting...
echo Backend: http://localhost:8000
echo Frontend: http://localhost:3000
echo.
echo Press any key to close this window...
pause > nul
