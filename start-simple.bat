@echo off
echo Simple NVH Django Development Startup
echo.

echo Step 1: Installing frontend dependencies...
cd frontend
call npm install
if errorlevel 1 (
    echo Error: Failed to install frontend dependencies
    pause
    exit /b 1
)

echo.
echo Step 2: Setting up backend...
cd ..\backend

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo Error: Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM 激活虚拟环境并安装依赖
echo Installing backend dependencies...
call venv\Scripts\activate
call pip install -r requirements.txt
if errorlevel 1 (
    echo Error: Failed to install backend dependencies
    pause
    exit /b 1
)

echo.
echo Step 3: Database setup...
call python manage.py makemigrations
call python manage.py migrate
if errorlevel 1 (
    echo Error: Database migration failed
    pause
    exit /b 1
)

echo.
echo Setup completed successfully!
echo.
echo To start the servers manually:
echo 1. Backend: cd backend ^&^& venv\Scripts\activate ^&^& python manage.py runserver 0.0.0.0:8000
echo 2. Frontend: cd frontend ^&^& npm run serve
echo.
echo Or run start-servers.bat to start both automatically
pause
