version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: nvh_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: nvh_data
      MYSQL_USER: nvh_user
      MYSQL_PASSWORD: nvh_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - nvh_network

  # Redis (可选，用于缓存)
  redis:
    image: redis:7-alpine
    container_name: nvh_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - nvh_network

  # Django 后端
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: nvh_backend
    restart: unless-stopped
    environment:
      - DEBUG=False
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=nvh_user
      - MYSQL_PASSWORD=nvh_password
      - MYSQL_DATABASE=nvh_data
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      - mysql
      - redis
    networks:
      - nvh_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             gunicorn nvh_django.wsgi:application --bind 0.0.0.0:8000"

  # Vue 前端 (开发环境)
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: nvh_frontend_dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - VUE_APP_API_BASE_URL=http://localhost:8000/api
    networks:
      - nvh_network
    profiles:
      - dev

  # Nginx (生产环境)
  nginx:
    image: nginx:alpine
    container_name: nvh_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - static_volume:/var/www/static
      - media_volume:/var/www/media
      - ./frontend/dist:/var/www/html
    depends_on:
      - backend
    networks:
      - nvh_network
    profiles:
      - prod

volumes:
  mysql_data:
  redis_data:
  static_volume:
  media_volume:

networks:
  nvh_network:
    driver: bridge
