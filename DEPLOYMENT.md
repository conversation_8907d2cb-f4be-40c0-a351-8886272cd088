# NVH Django 项目部署指南

本文档详细说明了如何部署 NVH Django 项目的各种方式。

## 目录

1. [开发环境部署](#开发环境部署)
2. [Docker 部署](#docker-部署)
3. [生产环境部署](#生产环境部署)
4. [Keycloak 配置](#keycloak-配置)
5. [数据库初始化](#数据库初始化)
6. [故障排除](#故障排除)

## 开发环境部署

### 前置要求

- Python 3.9+
- Node.js 16+
- MySQL 8.0+
- Git

### 快速启动

#### Windows 系统

```bash
# 双击运行启动脚本
start-dev.bat
```

#### Linux/Mac 系统

```bash
# 给脚本执行权限
chmod +x start-dev.sh

# 运行启动脚本
./start-dev.sh
```

#### 手动启动

**1. 后端启动**

```bash
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库和 Keycloak 信息

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户（可选）
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver 0.0.0.0:8000
```

**2. 前端启动**

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

### 访问地址

- 前端应用: http://localhost:3000
- 后端 API: http://localhost:8000
- Django Admin: http://localhost:8000/admin

## Docker 部署

### 开发环境 Docker 部署

```bash
# 启动开发环境
docker-compose --profile dev up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose --profile dev down
```

### 生产环境 Docker 部署

```bash
# 构建前端生产版本
cd frontend
npm run build
cd ..

# 启动生产环境
docker-compose --profile prod up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose --profile prod down
```

### Docker 服务说明

- **mysql**: MySQL 8.0 数据库
- **redis**: Redis 缓存服务
- **backend**: Django 后端服务
- **frontend-dev**: Vue 开发服务器（仅开发环境）
- **nginx**: Nginx 反向代理（仅生产环境）

## 生产环境部署

### 服务器要求

- Ubuntu 20.04+ / CentOS 8+
- 4GB+ RAM
- 20GB+ 磁盘空间
- Docker & Docker Compose

### 部署步骤

**1. 服务器准备**

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker
```

**2. 项目部署**

```bash
# 克隆项目
git clone <repository-url>
cd nvh_django

# 配置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件

# 构建并启动服务
docker-compose --profile prod up -d

# 执行数据库迁移
docker-compose exec backend python manage.py migrate

# 创建超级用户
docker-compose exec backend python manage.py createsuperuser

# 收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput
```

**3. 配置域名和 SSL**

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx 配置

如果不使用 Docker，可以手动配置 Nginx：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        root /path/to/frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /path/to/backend/staticfiles/;
    }

    location /media/ {
        alias /path/to/backend/media/;
    }
}
```

## Keycloak 配置

### 客户端配置

**前端客户端 (front)**

1. 登录 Keycloak 管理控制台
2. 选择对应的 Realm (demo)
3. 创建新客户端：
   - Client ID: `front`
   - Client Protocol: `openid-connect`
   - Access Type: `public`
   - Valid Redirect URIs: `http://localhost:3000/*`, `https://your-domain.com/*`
   - Web Origins: `http://localhost:3000`, `https://your-domain.com`

**后端客户端 (backend)**

1. 创建新客户端：
   - Client ID: `backend`
   - Client Protocol: `openid-connect`
   - Access Type: `bearer-only`
   - 记录生成的 Client Secret

### 用户和角色配置

1. 创建角色：
   - `admin`: 管理员角色
   - `user`: 普通用户角色
   - `tester`: 测试人员角色

2. 创建用户并分配角色

3. 配置用户属性映射

## 数据库初始化

### 创建初始数据

```bash
# 进入 Django shell
python manage.py shell

# 创建系统配置
from apps.core.models import SystemConfig
SystemConfig.objects.create(
    key='system_name',
    value='NVH 测试数据管理系统',
    description='系统名称'
)

# 创建测试项目
from apps.testing.models import TestProject
TestProject.objects.create(
    name='示例项目',
    description='这是一个示例测试项目',
    project_type='modal',
    status='active',
    vehicle_model='示例车型'
)
```

### 数据库备份

```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p nvh_data > backup.sql

# 恢复数据库
docker-compose exec -i mysql mysql -u root -p nvh_data < backup.sql
```

## 监控和日志

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend-dev
docker-compose logs -f mysql

# 查看 Django 日志
tail -f backend/logs/django.log
```

### 性能监控

建议安装以下监控工具：

- **Prometheus + Grafana**: 系统监控
- **ELK Stack**: 日志分析
- **Sentry**: 错误追踪

## 故障排除

### 常见问题

**1. Keycloak 连接失败**

```bash
# 检查 Keycloak 服务状态
curl -I https://account-test.sgmw.com.cn/auth/

# 检查网络连接
ping account-test.sgmw.com.cn

# 验证证书
openssl s_client -connect account-test.sgmw.com.cn:443
```

**2. 数据库连接失败**

```bash
# 检查数据库服务
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"

# 检查连接配置
docker-compose exec backend python manage.py dbshell
```

**3. 前端无法访问后端**

```bash
# 检查后端服务状态
curl http://localhost:8000/api/core/health/

# 检查 CORS 配置
# 确保 CORS_ALLOWED_ORIGINS 包含前端地址
```

**4. 静态文件无法加载**

```bash
# 重新收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput

# 检查 Nginx 配置
docker-compose exec nginx nginx -t
```

### 日志级别调整

在 `backend/nvh_django/settings.py` 中调整日志级别：

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'DEBUG',  # 改为 DEBUG 获取更详细日志
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

### 性能优化

**数据库优化**

```sql
-- 添加索引
CREATE INDEX idx_project_type ON testing_project(project_type);
CREATE INDEX idx_test_date ON testing_modal_data(test_date);
CREATE INDEX idx_created_at ON testing_project(created_at);
```

**缓存配置**

在 Django settings 中启用 Redis 缓存：

```python
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## 维护和更新

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动服务
docker-compose --profile prod up -d --build

# 执行数据库迁移
docker-compose exec backend python manage.py migrate

# 收集静态文件
docker-compose exec backend python manage.py collectstatic --noinput
```

### 定期维护

1. **数据库备份**: 每日自动备份
2. **日志清理**: 定期清理旧日志文件
3. **系统更新**: 定期更新系统和依赖包
4. **安全检查**: 定期检查安全漏洞

## 联系支持

如遇到部署问题，请联系技术支持团队或查看项目文档。
