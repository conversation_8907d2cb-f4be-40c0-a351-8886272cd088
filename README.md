# NVH Django 项目

基于 Django + Vue.js 的 NVH（噪声、振动、声学）测试数据管理系统，集成 Keycloak 统一身份认证。

## 项目结构

```
nvh_django/
├── backend/                    # Django 后端
│   ├── manage.py              # Django 管理脚本
│   ├── nvh_django/            # Django 项目配置
│   ├── apps/                  # 应用模块
│   │   ├── authentication/   # 认证应用
│   │   ├── core/             # 核心应用
│   │   └── testing/          # 测试数据应用
│   ├── requirements.txt       # Python 依赖
│   └── .env.example          # 环境变量示例
├── frontend/                  # Vue.js 前端
│   ├── src/                  # 源码目录
│   │   ├── components/       # 组件
│   │   ├── views/           # 页面
│   │   ├── router/          # 路由配置
│   │   ├── store/           # 状态管理
│   │   └── utils/           # 工具函数
│   ├── package.json         # Node.js 依赖
│   └── vue.config.js        # Vue 配置
└── README.md                # 项目说明
```

## 技术栈

### 后端
- **Django 5.1+**: Web 框架
- **Django REST Framework**: API 框架
- **MySQL**: 数据库
- **Keycloak**: 身份认证
- **PyJWT**: JWT 处理
- **django-cors-headers**: 跨域处理

### 前端
- **Vue 3**: 前端框架
- **Vue Router 4**: 路由管理
- **Vuex 4**: 状态管理
- **Element Plus**: UI 组件库
- **Axios**: HTTP 客户端
- **Keycloak JS**: 前端认证
- **ECharts**: 图表库

## 快速开始

### 环境要求

- Python 3.9+
- Node.js 16+
- MySQL 8.0+
- Keycloak 服务器

### 1. 克隆项目

```bash
git clone <repository-url>
cd nvh_django
```

### 2. 后端设置

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置数据库和 Keycloak 信息
# 创建数据库迁移
python manage.py makemigrations

# 执行数据库迁移
python manage.py migrate

# 创建超级用户（可选）
python manage.py createsuperuser

# 启动开发服务器
python manage.py runserver 0.0.0.0:8000
```

### 3. 前端设置

```bash
# 新开终端，进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

### 4. 访问应用

- 前端应用: http://localhost:3000
- 后端 API: http://localhost:8000
- Django Admin: http://localhost:8000/admin

## 配置说明

### 后端配置 (.env)

```env
# Django 配置
SECRET_KEY=your-secret-key
DEBUG=True

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your-password
MYSQL_DATABASE=nvh_data

# Keycloak 配置
KEYCLOAK_SERVER_URL=https://account-test.sgmw.com.cn/auth/
KEYCLOAK_REALM=demo
KEYCLOAK_CLIENT_ID=backend
KEYCLOAK_CLIENT_SECRET=8545c061-7cf7-41e5-b92b-e6769a6a75b8
KEYCLOAK_FRONTEND_CLIENT_ID=front
```

### Keycloak 配置

系统使用 Keycloak 进行统一身份认证，需要配置以下客户端：

#### 前端客户端 (front)
- Client ID: `front`
- Client Protocol: `openid-connect`
- Access Type: `public`
- Valid Redirect URIs: `http://localhost:3000/*`

#### 后端客户端 (backend)
- Client ID: `backend`
- Client Protocol: `openid-connect`
- Access Type: `bearer-only`
- Client Secret: `8545c061-7cf7-41e5-b92b-e6769a6a75b8`

### 测试账号

- 用户名: `test`
- 密码: `B5FDs0PcyuTipj^！`
- Realm: `demo`

## 功能特性

### 已实现功能

1. **用户认证**
   - Keycloak 统一身份认证
   - JWT Token 管理
   - 自动 Token 刷新
   - 用户信息管理

2. **首页仪表板**
   - 项目统计概览
   - 项目类型分布图表
   - 最近活动记录
   - 快速操作入口

3. **系统架构**
   - 前后端分离架构
   - RESTful API 设计
   - 响应式 UI 设计
   - 模块化代码结构

### 待实现功能

1. **测试项目管理**
   - 项目 CRUD 操作
   - 项目状态管理
   - 项目详情页面

2. **测试数据管理**
   - 模态测试数据
   - 气密性测试数据
   - 其他测试类型数据

3. **文件管理**
   - 文件上传下载
   - 图片预览
   - 文档管理

## 开发指南

### 后端开发

1. **创建新应用**
```bash
cd backend
python manage.py startapp your_app_name
```

2. **添加 API 端点**
```python
# views.py
from rest_framework import viewsets
from .models import YourModel
from .serializers import YourSerializer

class YourViewSet(viewsets.ModelViewSet):
    queryset = YourModel.objects.all()
    serializer_class = YourSerializer
```

3. **数据库迁移**
```bash
python manage.py makemigrations
python manage.py migrate
```

### 前端开发

1. **创建新页面**
```bash
# 在 src/views/ 目录下创建 Vue 组件
```

2. **添加路由**
```javascript
// src/router/index.js
{
  path: '/your-path',
  component: () => import('@/views/YourComponent.vue')
}
```

3. **状态管理**
```javascript
// src/store/modules/your-module.js
export default {
  namespaced: true,
  state: {},
  mutations: {},
  actions: {},
  getters: {}
}
```

## 部署说明

### 生产环境部署

1. **后端部署**
```bash
# 安装生产依赖
pip install gunicorn

# 收集静态文件
python manage.py collectstatic

# 启动 Gunicorn
gunicorn nvh_django.wsgi:application --bind 0.0.0.0:8000
```

2. **前端部署**
```bash
# 构建生产版本
npm run build

# 部署到 Web 服务器
# 将 dist/ 目录内容部署到 Nginx 等 Web 服务器
```

### Docker 部署

```bash
# 构建镜像
docker build -t nvh-django .

# 运行容器
docker run -p 8000:8000 nvh-django
```

## 故障排除

### 常见问题

1. **Keycloak 认证失败**
   - 检查 Keycloak 服务器是否可访问
   - 验证客户端配置是否正确
   - 确认重定向 URI 配置

2. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确认数据库用户权限

3. **前端无法访问后端 API**
   - 检查 CORS 配置
   - 验证 API 地址是否正确
   - 确认后端服务是否启动

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系项目维护者。
