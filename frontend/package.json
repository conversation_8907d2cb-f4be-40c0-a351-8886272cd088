{"name": "nvh-frontend", "version": "1.0.0", "description": "NVH Django Frontend - Vue.js Application", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve --mode development", "build:prod": "vue-cli-service build --mode production"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.4.0", "vuex": "^4.1.0", "axios": "^1.7.0", "keycloak-js": "^25.0.0", "element-plus": "^2.8.0", "@element-plus/icons-vue": "^2.3.0", "echarts": "^5.5.0", "vue-echarts": "^7.0.0", "dayjs": "^1.11.0", "lodash": "^4.17.21", "path-browserify": "^1.0.1"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.0", "@vue/cli-plugin-router": "^5.0.0", "@vue/cli-plugin-vuex": "^5.0.0", "@vue/cli-service": "^5.0.0", "@vue/eslint-config-standard": "^8.0.0", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-vue": "^9.27.0", "sass": "^1.77.0", "sass-loader": "^16.0.0", "webpack": "^5.88.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}