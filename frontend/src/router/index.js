import { createRouter, createWebHistory } from 'vue-router'
import { isAuthenticated } from '@/utils/keycloak'

// 路由组件懒加载
const Layout = () => import('@/layout/index.vue')
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/Login.vue')
const NotFound = () => import('@/views/404.vue')

// 测试模块路由
const TestProjects = () => import('@/views/testing/Projects.vue')
const ProjectDetail = () => import('@/views/testing/ProjectDetail.vue')
const ModalTest = () => import('@/views/testing/ModalTest.vue')
const AirtightnessTest = () => import('@/views/testing/AirtightnessTest.vue')

// 系统管理路由
const UserProfile = () => import('@/views/system/UserProfile.vue')
const SystemSettings = () => import('@/views/system/Settings.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'home',
        name: 'Home',
        component: Home,
        meta: {
          title: '首页',
          icon: 'House'
        }
      }
    ]
  },
  {
    path: '/testing',
    component: Layout,
    redirect: '/testing/projects',
    meta: {
      title: '测试管理',
      icon: 'DataAnalysis',
      requiresAuth: true
    },
    children: [
      {
        path: 'projects',
        name: 'TestProjects',
        component: TestProjects,
        meta: {
          title: '测试项目',
          icon: 'FolderOpened'
        }
      },
      {
        path: 'projects/:id',
        name: 'ProjectDetail',
        component: ProjectDetail,
        meta: {
          title: '项目详情',
          hidden: true
        }
      },
      {
        path: 'modal',
        name: 'ModalTest',
        component: ModalTest,
        meta: {
          title: '模态测试',
          icon: 'TrendCharts'
        }
      },
      {
        path: 'airtightness',
        name: 'AirtightnessTest',
        component: AirtightnessTest,
        meta: {
          title: '气密性测试',
          icon: 'WindPower'
        }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    redirect: '/system/profile',
    meta: {
      title: '系统管理',
      icon: 'Setting',
      requiresAuth: true
    },
    children: [
      {
        path: 'profile',
        name: 'UserProfile',
        component: UserProfile,
        meta: {
          title: '个人资料',
          icon: 'User'
        }
      },
      {
        path: 'settings',
        name: 'SystemSettings',
        component: SystemSettings,
        meta: {
          title: '系统设置',
          icon: 'Tools'
        }
      }
    ]
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面不存在'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - NVH 系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!isAuthenticated()) {
      // 未认证，跳转到登录页
      next('/login')
      return
    }
  }

  next()
})

export default router
