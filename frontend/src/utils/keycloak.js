import Keycloak from 'keycloak-js'
import axios from 'axios'

let keycloakInstance = null

/**
 * 获取 Keycloak 配置
 */
async function getKeycloakConfig() {
  try {
    const response = await axios.get('/api/auth/config/')
    return response.data
  } catch (error) {
    console.error('Failed to fetch Keycloak config:', error)
    // 使用默认配置
    return {
      url: 'https://account-test.sgmw.com.cn/auth/',
      realm: 'demo',
      clientId: 'front'
    }
  }
}

/**
 * 初始化 Keycloak
 */
export async function initKeycloak() {
  if (keycloakInstance) {
    return keycloakInstance
  }

  try {
    const config = await getKeycloakConfig()
    
    keycloakInstance = new Keycloak({
      url: config.url,
      realm: config.realm,
      clientId: config.clientId
    })

    const authenticated = await keycloakInstance.init({
      onLoad: 'login-required',
      checkLoginIframe: false,
      pkceMethod: 'S256'
    })

    if (authenticated) {
      console.log('Keycloak authentication successful')
      
      // 设置 token 刷新
      setupTokenRefresh()
      
      // 设置 axios 拦截器
      setupAxiosInterceptors()
      
      return keycloakInstance
    } else {
      throw new Error('Keycloak authentication failed')
    }
  } catch (error) {
    console.error('Keycloak initialization error:', error)
    throw error
  }
}

/**
 * 设置 token 自动刷新
 */
function setupTokenRefresh() {
  // 每 60 秒检查一次 token 是否需要刷新
  setInterval(() => {
    keycloakInstance.updateToken(70).then((refreshed) => {
      if (refreshed) {
        console.log('Token refreshed')
      } else {
        const timeLeft = Math.round(
          keycloakInstance.tokenParsed.exp + 
          keycloakInstance.timeSkew - 
          new Date().getTime() / 1000
        )
        console.log(`Token not refreshed, valid for ${timeLeft} seconds`)
      }
    }).catch((error) => {
      console.error('Failed to refresh token:', error)
      // Token 刷新失败，重新登录
      keycloakInstance.login()
    })
  }, 60000)
}

/**
 * 设置 Axios 拦截器
 */
function setupAxiosInterceptors() {
  // 请求拦截器 - 添加 Authorization header
  axios.interceptors.request.use(
    (config) => {
      if (keycloakInstance && keycloakInstance.authenticated) {
        config.headers.Authorization = `Bearer ${keycloakInstance.token}`
      }
      config.headers['Content-Type'] = 'application/json'
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器 - 处理认证错误
  axios.interceptors.response.use(
    (response) => {
      return response
    },
    (error) => {
      if (error.response && error.response.status === 401) {
        console.log('Unauthorized, redirecting to login')
        keycloakInstance.login()
      }
      return Promise.reject(error)
    }
  )
}

/**
 * 获取 Keycloak 实例
 */
export function getKeycloak() {
  return keycloakInstance
}

/**
 * 登出
 */
export function logout() {
  if (keycloakInstance) {
    keycloakInstance.logout({
      redirectUri: window.location.origin
    })
  }
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  if (keycloakInstance && keycloakInstance.authenticated) {
    return {
      id: keycloakInstance.tokenParsed.sub,
      username: keycloakInstance.tokenParsed.preferred_username,
      email: keycloakInstance.tokenParsed.email,
      name: keycloakInstance.tokenParsed.name,
      roles: keycloakInstance.tokenParsed.realm_access?.roles || []
    }
  }
  return null
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(role) {
  if (keycloakInstance && keycloakInstance.authenticated) {
    return keycloakInstance.hasRealmRole(role)
  }
  return false
}

/**
 * 检查用户是否已认证
 */
export function isAuthenticated() {
  return keycloakInstance && keycloakInstance.authenticated
}
