import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import './styles/index.scss'
import { initKeycloak } from './utils/keycloak'

// 初始化 Keycloak
initKeycloak().then((keycloak) => {
  const app = createApp(App)
  
  // 注册 Element Plus 图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  
  // 配置 Element Plus
  app.use(ElementPlus, {
    locale: zhCn,
    size: 'default'
  })
  
  // 配置路由和状态管理
  app.use(store)
  app.use(router)
  
  // 全局属性
  app.config.globalProperties.$keycloak = keycloak
  
  // 挂载应用
  app.mount('#app')
  
  console.log('NVH System initialized successfully')
}).catch(error => {
  console.error('Failed to initialize Keycloak:', error)
  
  // 如果 Keycloak 初始化失败，仍然启动应用但显示错误信息
  const app = createApp(App)
  
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  
  app.use(ElementPlus, { locale: zhCn })
  app.use(store)
  app.use(router)
  
  app.mount('#app')
})
