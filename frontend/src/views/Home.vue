<template>
  <div class="dashboard-container">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <el-card class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎使用 NVH 测试数据管理系统</h2>
            <p>您好，{{ userInfo.username }}！今天是 {{ currentDate }}</p>
          </div>
          <div class="welcome-actions">
            <el-button type="primary" @click="$router.push('/testing/projects')">
              <el-icon><FolderOpened /></el-icon>
              查看测试项目
            </el-button>
            <el-button @click="$router.push('/testing/modal')">
              <el-icon><TrendCharts /></el-icon>
              模态测试
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon size="32"><DataAnalysis /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardData.total_projects || 0 }}</div>
                <div class="stats-label">总项目数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon active">
                <el-icon size="32"><Loading /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardData.active_projects || 0 }}</div>
                <div class="stats-label">进行中项目</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon completed">
                <el-icon size="32"><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardData.completed_projects || 0 }}</div>
                <div class="stats-label">已完成项目</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :lg="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon activities">
                <el-icon size="32"><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ dashboardData.recent_activities?.length || 0 }}</div>
                <div class="stats-label">最近活动</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 内容区域 -->
    <el-row :gutter="20" class="content-section">
      <!-- 项目类型统计 -->
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>项目类型分布</span>
            </div>
          </template>
          <div class="chart-container">
            <project-type-chart :data="dashboardData.project_stats_by_type || []" />
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :xs="24" :lg="12">
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button text @click="$router.push('/system/profile')">查看更多</el-button>
            </div>
          </template>
          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon><Operation /></el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.action }}</div>
                <div class="activity-desc">{{ activity.resource }} - {{ activity.user }}</div>
                <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
              </div>
            </div>
            <div v-if="!recentActivities.length" class="no-activity">
              暂无活动记录
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { request } from '@/utils/request'
import ProjectTypeChart from '@/components/charts/ProjectTypeChart.vue'
import dayjs from 'dayjs'

export default {
  name: 'Home',
  components: {
    ProjectTypeChart
  },
  setup() {
    const store = useStore()
    const dashboardData = ref({})
    const loading = ref(false)
    
    const userInfo = computed(() => store.getters['user/userInfo'] || {})
    const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))
    
    const recentActivities = computed(() => {
      return (dashboardData.value.recent_activities || []).slice(0, 8)
    })
    
    const fetchDashboardData = async () => {
      loading.value = true
      try {
        const data = await request.get('/core/dashboard/')
        dashboardData.value = data
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
      } finally {
        loading.value = false
      }
    }
    
    const formatTime = (timestamp) => {
      return dayjs(timestamp).format('MM-DD HH:mm')
    }
    
    onMounted(() => {
      fetchDashboardData()
      // 获取用户信息
      if (!userInfo.value.username) {
        store.dispatch('user/fetchUserInfo')
      }
    })
    
    return {
      dashboardData,
      loading,
      userInfo,
      currentDate,
      recentActivities,
      formatTime
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  
  .welcome-section {
    margin-bottom: 20px;
    
    .welcome-card {
      .welcome-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .welcome-text {
          h2 {
            margin: 0 0 8px 0;
            color: #303133;
            font-size: 24px;
          }
          
          p {
            margin: 0;
            color: #606266;
            font-size: 14px;
          }
        }
        
        .welcome-actions {
          .el-button + .el-button {
            margin-left: 12px;
          }
        }
      }
    }
  }
  
  .stats-section {
    margin-bottom: 20px;
    
    .stats-card {
      .stats-content {
        display: flex;
        align-items: center;
        
        .stats-icon {
          width: 60px;
          height: 60px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          
          &.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
          
          &.active {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
          }
          
          &.completed {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
          }
          
          &.activities {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
          }
        }
        
        .stats-info {
          .stats-number {
            font-size: 28px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
          }
          
          .stats-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .content-section {
    .chart-card,
    .activity-card {
      height: 400px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .chart-container {
        height: 320px;
      }
    }
    
    .activity-list {
      height: 320px;
      overflow-y: auto;
      
      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 32px;
          height: 32px;
          background: #f5f7fa;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;
          
          .el-icon {
            color: #909399;
          }
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .activity-desc {
            font-size: 12px;
            color: #909399;
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: #c0c4cc;
          }
        }
      }
      
      .no-activity {
        text-align: center;
        color: #909399;
        padding: 40px 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
    
    .welcome-section .welcome-card .welcome-content {
      flex-direction: column;
      align-items: flex-start;
      
      .welcome-actions {
        margin-top: 16px;
      }
    }
  }
}
</style>
