const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 开发服务器配置
  devServer: {
    port: 3000,
    host: '0.0.0.0',
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        logLevel: 'debug'
      }
    }
  },
  
  // 构建配置
  publicPath: process.env.NODE_ENV === 'production' ? '/static/' : '/',
  outputDir: 'dist',
  assetsDir: 'assets',
  
  // CSS 配置
  css: {
    loaderOptions: {
      sass: {
        additionalData: `
          // 基础颜色变量
          $blue: #409EFF;
          $green: #67C23A;
          $red: #F56C6C;
          $yellow: #E6A23C;
          $primary-color: $blue;
          $success-color: $green;
          $warning-color: $yellow;
          $danger-color: $red;
        `
      }
    }
  },
  
  // 链式操作配置
  chainWebpack: config => {
    // 设置别名
    config.resolve.alias
      .set('@', require('path').resolve(__dirname, 'src'))
    
    // 优化构建
    if (process.env.NODE_ENV === 'production') {
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'chunk-vendors',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          elementPlus: {
            name: 'chunk-element-plus',
            priority: 20,
            test: /[\\/]node_modules[\\/]element-plus[\\/]/
          }
        }
      })
    }
  },
  
  // PWA 配置（可选）
  pwa: {
    name: 'NVH System',
    themeColor: '#409EFF',
    msTileColor: '#000000',
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black'
  }
})
